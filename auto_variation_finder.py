#!/usr/bin/env python3
"""
Automatic Image Variation Finder and Combiner

A Python tool that automatically finds similar image variations and combines them
into batches of 4 images per composite.
"""

import os
import sys
from PIL import Image
import argparse
from typing import List, Tuple, Dict, Set, Optional, Callable
from collections import defaultdict
import glob
from image_combiner import ImageCombiner
import shutil
import numpy as np
import math
import time
import random
import gc
import threading
import psutil
try:
    import torch
    import clip
    from sklearn.metrics.pairwise import cosine_similarity
    CLIP_AVAILABLE = True
except ImportError:
    CLIP_AVAILABLE = False
    print("Error: CLIP not available. Install with: pip install torch torchvision clip-by-openai scikit-learn")
    print("CLIP is required for this application.")

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


class PerformanceMonitor:
    """Monitor performance metrics and provide progress tracking for large dataset operations."""

    def __init__(self, total_operations: int = 0, operation_name: str = "Processing"):
        self.total_operations = total_operations
        self.operation_name = operation_name
        self.start_time = time.time()
        self.last_update_time = self.start_time
        self.completed_operations = 0
        self.progress_callback = None
        self.timeout_seconds = None
        self.early_termination_flag = threading.Event()

    def set_progress_callback(self, callback: Callable[[str], None]):
        """Set a callback function for progress updates."""
        self.progress_callback = callback

    def set_timeout(self, timeout_seconds: int):
        """Set a timeout for the operation."""
        self.timeout_seconds = timeout_seconds

    def signal_early_termination(self):
        """Signal that the operation should terminate early."""
        self.early_termination_flag.set()

    def should_terminate_early(self) -> bool:
        """Check if the operation should terminate early due to timeout or signal."""
        if self.early_termination_flag.is_set():
            return True

        if self.timeout_seconds and (time.time() - self.start_time) > self.timeout_seconds:
            return True

        return False

    def update_progress(self, completed: int, details: str = ""):
        """Update progress and report if necessary."""
        self.completed_operations = completed
        current_time = time.time()

        # Report progress every 5 seconds or at significant milestones
        time_since_update = current_time - self.last_update_time
        progress_pct = (completed / self.total_operations * 100) if self.total_operations > 0 else 0

        should_report = (
            time_since_update >= 5.0 or  # Every 5 seconds
            completed % max(1, self.total_operations // 20) == 0 or  # Every 5% progress
            completed == self.total_operations  # Final update
        )

        if should_report:
            elapsed = current_time - self.start_time
            if completed > 0 and self.total_operations > 0:
                remaining_time = (elapsed / completed) * (self.total_operations - completed)
                eta_str = f", ETA: {remaining_time:.1f}s"
            else:
                eta_str = ""

            memory_info = self.get_memory_usage()
            message = f"{self.operation_name}: {completed}/{self.total_operations} ({progress_pct:.1f}%) - {elapsed:.1f}s elapsed{eta_str} - {memory_info}"
            if details:
                message += f" - {details}"

            print(message)
            if self.progress_callback:
                self.progress_callback(message)

            self.last_update_time = current_time

    def get_memory_usage(self) -> str:
        """Get current memory usage information."""
        if PSUTIL_AVAILABLE:
            try:
                process = psutil.Process()
                memory_mb = process.memory_info().rss / 1024 / 1024
                return f"Memory: {memory_mb:.1f}MB"
            except:
                pass
        return "Memory: N/A"

    def get_final_report(self) -> str:
        """Get a final performance report."""
        total_time = time.time() - self.start_time
        memory_info = self.get_memory_usage()

        if self.should_terminate_early():
            status = "TERMINATED EARLY"
        else:
            status = "COMPLETED"

        return f"{self.operation_name} {status}: {self.completed_operations}/{self.total_operations} operations in {total_time:.1f}s - {memory_info}"
    sys.exit(1)

class ImageVariationFinder:
    def __init__(self, clip_threshold: float = 0.85, clip_model_name: str = "ViT-B/32", device: str = "auto", progress_callback: Optional[Callable[[str], None]] = None):
        """
        Initialize the variation finder.

        Args:
            clip_threshold: Minimum cosine similarity for CLIP (0.0-1.0, higher = more strict)
            clip_model_name: CLIP model to use ("ViT-B/32", "ViT-L/14", "RN50", etc.)
            device: Device to use ("auto", "cpu", "gpu", "mps", "cuda")
            progress_callback: Optional callback function for progress updates
        """
        self.clip_threshold = clip_threshold
        self.clip_model_name = clip_model_name
        self.image_features = {}
        self.image_paths = []
        self.progress_callback = progress_callback
        self.performance_monitor = None

        # Performance settings for large datasets
        self.enable_memory_optimization = True
        self.enable_early_termination = True
        self.max_refinement_time_minutes = 15  # Maximum time for global refinement
        self.feature_cache_limit = 5000  # Maximum number of features to keep in memory
        self._similarity_matrix_cache = None  # Cache for reuse

        # Initialize CLIP model
        if not CLIP_AVAILABLE:
            raise RuntimeError("CLIP is required but not available. Install with: pip install torch torchvision clip-by-openai scikit-learn")
        
        try:
            print(f"Loading CLIP model: {self.clip_model_name}...")
            self.device = self._select_device(device)
            self.clip_model, self.clip_preprocess = clip.load(self.clip_model_name, device=self.device)
            print(f"CLIP model {self.clip_model_name} loaded on {self.device}")
        except Exception as e:
            raise RuntimeError(f"Failed to load CLIP model: {e}")
    
    def _select_device(self, device_preference: str) -> str:
        """
        Select the appropriate device based on preference and availability.
        
        Args:
            device_preference: User's device preference
            
        Returns:
            str: Selected device name
        """
        if device_preference == "cpu":
            return "cpu"
        elif device_preference == "auto":
            # Auto-select best available device
            if torch.backends.mps.is_available():
                return "mps"
            elif torch.cuda.is_available():
                return "cuda"
            else:
                return "cpu"
        elif device_preference in ["gpu", "mps"]:
            if torch.backends.mps.is_available():
                return "mps"
            else:
                print("Warning: MPS not available, falling back to CPU")
                return "cpu"
        elif device_preference == "cuda":
            if torch.cuda.is_available():
                return "cuda"
            else:
                print("Warning: CUDA not available, falling back to CPU")
                return "cpu"
        else:
            print(f"Warning: Unknown device '{device_preference}', using auto-selection")
            return self._select_device("auto")

    def configure_performance_settings(self, max_refinement_time_minutes: int = 15,
                                     enable_memory_optimization: bool = True,
                                     enable_early_termination: bool = True):
        """
        Configure performance settings for large dataset processing.

        Args:
            max_refinement_time_minutes: Maximum time to spend on global refinement
            enable_memory_optimization: Enable memory optimization techniques
            enable_early_termination: Enable early termination on timeout/signal
        """
        self.max_refinement_time_minutes = max_refinement_time_minutes
        self.enable_memory_optimization = enable_memory_optimization
        self.enable_early_termination = enable_early_termination

    def set_progress_callback(self, callback: Callable[[str], None]):
        """Set a callback function for progress updates."""
        self.progress_callback = callback

    def _manage_feature_cache(self):
        """Manage feature cache to prevent memory overflow."""
        if len(self.image_features) > self.feature_cache_limit:
            print(f"Feature cache limit reached ({self.feature_cache_limit}), clearing oldest entries...")
            # Keep only the most recent features (simple FIFO)
            items = list(self.image_features.items())
            self.image_features = dict(items[-self.feature_cache_limit//2:])
            gc.collect()

    def _cleanup_memory(self):
        """Clean up memory by removing cached data."""
        if self.enable_memory_optimization:
            self.image_features.clear()
            self._similarity_matrix_cache = None
            gc.collect()
            print("Memory cleanup completed")

    def extract_clip_features(self, image_path: str) -> np.ndarray:
        """
        Extract CLIP features for an image.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            CLIP feature vector as numpy array
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Preprocess image for CLIP
                image_input = self.clip_preprocess(img).unsqueeze(0).to(self.device)
                
                # Extract features
                with torch.no_grad():
                    image_features = self.clip_model.encode_image(image_input)
                    # Normalize features
                    image_features = image_features / image_features.norm(dim=-1, keepdim=True)
                
                return image_features.cpu().numpy().flatten()
        except Exception as e:
            print(f"Error extracting CLIP features from {image_path}: {e}")
            return None
    
    def scan_directory(self, directory: str, extensions: List[str] = None) -> List[str]:
        """
        Scan directory for image files.
        
        Args:
            directory: Directory to scan
            extensions: List of file extensions to include
            
        Returns:
            List of image file paths
        """
        if extensions is None:
            extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp']
        
        image_files = []
        for ext in extensions:
            pattern = os.path.join(directory, f"**/*{ext}")
            image_files.extend(glob.glob(pattern, recursive=True))
            pattern = os.path.join(directory, f"**/*{ext.upper()}")
            image_files.extend(glob.glob(pattern, recursive=True))
        
        return sorted(list(set(image_files)))
    
    def find_similar_images(self, image_paths: List[str]) -> Dict[str, List[str]]:
        """
        Find groups of similar images using CLIP.
        
        Args:
            image_paths: List of image file paths
            
        Returns:
            Dictionary mapping representative image to list of similar images
        """
        print(f"Analyzing {len(image_paths)} images for similarity...")
        return self._find_similar_images_clip(image_paths)
    
    def _find_similar_images_clip(self, image_paths: List[str]) -> Dict[str, List[str]]:
        """
        Find similar images using CLIP features with improved clustering algorithm.
        """
        print("Using CLIP for similarity detection...")

        # Apply dynamic performance scaling
        num_images = len(image_paths)
        optimization_settings = self.optimize_for_dataset_size(num_images)
        performance_strategy = optimization_settings.get("performance_strategy", "standard")

        print(f"Dataset size: {num_images} images - Performance strategy: {performance_strategy}")
        print(f"Optimization settings: {optimization_settings}")

        # Apply dynamic settings
        self.max_refinement_time_minutes = optimization_settings["max_refinement_time_minutes"]

        # Extract CLIP features for all images with progress tracking
        valid_images = []
        feature_matrix = []

        # Process in batches for large datasets to manage memory (adaptive batch size)
        if performance_strategy == "ultra_large":
            batch_size = 25
        elif performance_strategy == "large":
            batch_size = 50
        elif performance_strategy == "medium":
            batch_size = 75
        else:
            batch_size = len(image_paths)  # Process all at once for small datasets

        for batch_start in range(0, len(image_paths), batch_size):
            batch_end = min(batch_start + batch_size, len(image_paths))
            batch_paths = image_paths[batch_start:batch_end]

            print(f"Processing batch {batch_start//batch_size + 1}/{math.ceil(len(image_paths)/batch_size)} ({batch_end - batch_start} images)...")

            for i, path in enumerate(batch_paths):
                global_idx = batch_start + i
                if (global_idx + 1) % 100 == 0 or global_idx == len(image_paths) - 1:
                    print(f"Processed {global_idx + 1}/{len(image_paths)} images...")

                features = self.extract_clip_features(path)
                if features is not None:
                    self.image_features[path] = features
                    valid_images.append(path)
                    feature_matrix.append(features)

                    # Manage feature cache for large datasets
                    if self.enable_memory_optimization and len(self.image_features) % 1000 == 0:
                        self._manage_feature_cache()
                else:
                    print(f"Skipping invalid image: {path}")

        if len(valid_images) < 2:
            print("Not enough valid images for similarity comparison")
            return {}

        print(f"Successfully processed {len(valid_images)} images")

        # Convert to numpy array for efficient computation
        feature_matrix = np.array(feature_matrix)

        # Calculate cosine similarity matrix with memory optimization for large datasets
        print("Calculating similarity matrix...")

        # Use dynamic chunking based on performance strategy
        use_chunked_calculation = (
            performance_strategy in ["large", "ultra_large"] and len(valid_images) > 1000
        ) or len(valid_images) > 2000

        if use_chunked_calculation:
            # For very large datasets, use chunked similarity calculation to manage memory
            print(f"Using chunked similarity calculation for {len(valid_images)} images...")
            chunk_size = optimization_settings.get("chunk_size", 200)
            similarity_matrix = self._calculate_chunked_similarity(feature_matrix, chunk_size)
        else:
            similarity_matrix = cosine_similarity(feature_matrix)

        # Store for use in intelligent batch splitting
        self._current_similarity_matrix = similarity_matrix
        self.image_paths = valid_images

        # Use improved clustering algorithm with dynamic settings
        groups = self._cluster_similar_images(valid_images, similarity_matrix, optimization_settings)

        print(f"Total groups created: {len(groups)}")

        # Memory cleanup for large datasets
        if self.enable_memory_optimization and performance_strategy in ["large", "ultra_large"]:
            print("Performing memory cleanup after similarity detection...")
            # Keep similarity matrix cached for potential reuse
            self._similarity_matrix_cache = similarity_matrix
            # Clear feature cache to free memory
            self.image_features.clear()
            gc.collect()

        return groups

    def _cluster_similar_images(self, valid_images: List[str], similarity_matrix: np.ndarray, optimization_settings: Dict[str, any] = None) -> Dict[str, List[str]]:
        """
        Improved clustering algorithm that creates more accurate similarity groups.
        Uses connected components approach with global similarity search to ensure
        all similar images are found across the entire dataset.

        Args:
            valid_images: List of valid image paths
            similarity_matrix: Precomputed similarity matrix
            optimization_settings: Dynamic optimization settings

        Returns:
            Dictionary mapping representative image paths to lists of similar images
        """
        print(f"Starting global similarity clustering with threshold: {self.clip_threshold}")
        print(f"Processing {len(valid_images)} images for comprehensive similarity detection...")

        # Step 1: Create adjacency matrix for similarity graph
        n = len(valid_images)
        adjacency = np.zeros((n, n), dtype=bool)

        # Build adjacency matrix based on similarity threshold
        similarity_pairs = 0
        for i in range(n):
            for j in range(i + 1, n):
                if similarity_matrix[i][j] >= self.clip_threshold:
                    adjacency[i][j] = True
                    adjacency[j][i] = True
                    similarity_pairs += 1

        print(f"Found {similarity_pairs} similarity pairs above threshold {self.clip_threshold}")

        # Step 2: Use Union-Find for more efficient connected components
        groups = self._find_connected_components_union_find(valid_images, adjacency, similarity_matrix)

        # Step 3: Apply global similarity refinement based on optimization settings
        if optimization_settings and optimization_settings.get("enable_global_refinement", True):
            if len(valid_images) > 100:  # For large datasets
                print(f"\n🔍 Step 3: Global similarity refinement for {len(valid_images)} images...")
                print(f"Initial groups found: {len(groups)}")
                print("This step may take some time for large datasets - please be patient...")
                groups = self._refine_groups_global_search(valid_images, similarity_matrix, groups)
        else:
            print("Skipping global refinement based on optimization settings")

        return groups

    def _find_connected_components_union_find(self, valid_images: List[str], adjacency: np.ndarray, similarity_matrix: np.ndarray) -> Dict[str, List[str]]:
        """
        Use Union-Find algorithm for efficient connected components detection.
        More efficient than DFS for large datasets.
        """
        n = len(valid_images)
        parent = list(range(n))
        rank = [0] * n

        def find(x):
            if parent[x] != x:
                parent[x] = find(parent[x])  # Path compression
            return parent[x]

        def union(x, y):
            px, py = find(x), find(y)
            if px == py:
                return
            # Union by rank
            if rank[px] < rank[py]:
                parent[px] = py
            elif rank[px] > rank[py]:
                parent[py] = px
            else:
                parent[py] = px
                rank[px] += 1

        # Union all similar pairs
        for i in range(n):
            for j in range(i + 1, n):
                if adjacency[i][j]:
                    union(i, j)

        # Group by root parent
        components = {}
        for i in range(n):
            root = find(i)
            if root not in components:
                components[root] = []
            components[root].append(i)

        # Convert to groups with representatives
        groups = {}
        for component_indices in components.values():
            if len(component_indices) > 1:
                # Find the most representative image (highest average similarity to others in group)
                best_representative = None
                best_avg_similarity = -1

                for candidate_idx in component_indices:
                    # Calculate average similarity to other images in the group
                    similarities = [similarity_matrix[candidate_idx][other_idx]
                                  for other_idx in component_indices if other_idx != candidate_idx]
                    avg_similarity = np.mean(similarities) if similarities else 0

                    if avg_similarity > best_avg_similarity:
                        best_avg_similarity = avg_similarity
                        best_representative = candidate_idx

                # Create group with representative as key
                representative_path = valid_images[best_representative]
                group_paths = [valid_images[idx] for idx in component_indices]
                groups[representative_path] = group_paths

                print(f"Found group of {len(component_indices)} similar images (avg similarity: {best_avg_similarity:.3f}, representative: {os.path.basename(representative_path)})")

            elif len(component_indices) == 1:
                # Single image group
                single_path = valid_images[component_indices[0]]
                groups[single_path] = [single_path]
                print(f"Created single-image group for: {os.path.basename(single_path)}")

        return groups

    def _refine_groups_global_search(self, valid_images: List[str], similarity_matrix: np.ndarray, initial_groups: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """
        Refine groups using global similarity search to catch missed connections.
        This addresses the issue where similar images might be in different groups.
        Optimized for large datasets with progress tracking and early termination.
        """
        print("Performing global similarity refinement for large dataset...")

        # Initialize performance monitor
        num_images = len(valid_images)
        total_groups = len(initial_groups)
        monitor = PerformanceMonitor(total_groups, "Global Refinement")
        monitor.set_progress_callback(self.progress_callback)

        # Set timeout based on configuration and dataset size
        timeout_seconds = self.max_refinement_time_minutes * 60
        if num_images > 1000:
            # For very large datasets, use optimized approach
            print(f"⚠️  Very large dataset ({num_images} images) - using optimized refinement")
            return self._refine_groups_optimized(valid_images, similarity_matrix, initial_groups, monitor)

        monitor.set_timeout(timeout_seconds)
        print(f"Setting timeout: {timeout_seconds//60} minutes for {num_images} images")

        # Create a lower threshold for refinement (catch near-misses)
        refinement_threshold = max(0.7, self.clip_threshold - 0.05)
        print(f"Using refinement threshold: {refinement_threshold:.3f}")

        # Track which images have been processed
        processed_images = set()
        refined_groups = {}

        # Create image index lookup for performance
        image_to_idx = {img: idx for idx, img in enumerate(valid_images)}
        processed_groups = 0

        # Process each group and look for global connections
        for representative, group_images in initial_groups.items():
            # Check for early termination
            if monitor.should_terminate_early():
                print(f"⚠️  Early termination triggered - stopping refinement")
                print(f"Processed {processed_groups}/{total_groups} groups before termination")
                # Return what we have so far, including unprocessed groups
                for rep, group in initial_groups.items():
                    if rep not in refined_groups and rep not in processed_images:
                        refined_groups[rep] = group
                break

            if representative in processed_images:
                continue

            processed_groups += 1

            # Update progress with detailed information
            group_details = f"Group size: {len(group_images)}"
            monitor.update_progress(processed_groups, group_details)

            # Start with the current group
            expanded_group = set(group_images)
            original_size = len(expanded_group)

            # Use optimized vectorized approach for finding similar images
            expanded_group = self._expand_group_vectorized(
                group_images, valid_images, similarity_matrix,
                image_to_idx, processed_images, refinement_threshold
            )

            # Mark all images in this expanded group as processed
            processed_images.update(expanded_group)

            # Create the refined group
            if len(expanded_group) > 1:
                # Find the best representative for the expanded group
                best_representative = self._find_best_representative(expanded_group, image_to_idx, similarity_matrix)
                refined_groups[best_representative] = list(expanded_group)

                if len(expanded_group) > original_size:
                    print(f"✅ Expanded group from {original_size} to {len(expanded_group)} images")
            else:
                refined_groups[representative] = list(expanded_group)

        # Final progress update and cleanup
        monitor.update_progress(processed_groups, "Finalizing groups")
        final_report = monitor.get_final_report()
        print(final_report)
        print(f"Global refinement complete: {len(initial_groups)} -> {len(refined_groups)} groups")

        # Memory cleanup for large datasets
        if self.enable_memory_optimization and num_images > 500:
            gc.collect()

        return refined_groups

    def _find_best_representative(self, group: set, image_to_idx: dict, similarity_matrix: np.ndarray) -> str:
        """Find the best representative for a group based on average similarity."""
        best_representative = None
        best_avg_similarity = -1

        for candidate_path in group:
            if candidate_path not in image_to_idx:
                continue
            candidate_idx = image_to_idx[candidate_path]

            # Calculate average similarity to other images in the group using vectorized operations
            other_indices = [image_to_idx[other_path] for other_path in group
                           if other_path != candidate_path and other_path in image_to_idx]

            if other_indices:
                similarities = similarity_matrix[candidate_idx][other_indices]
                avg_similarity = np.mean(similarities)

                if avg_similarity > best_avg_similarity:
                    best_avg_similarity = avg_similarity
                    best_representative = candidate_path

        return best_representative or next(iter(group))

    def _expand_group_vectorized(self, group_images: List[str], valid_images: List[str],
                               similarity_matrix: np.ndarray, image_to_idx: Dict[str, int],
                               processed_images: set, refinement_threshold: float) -> set:
        """
        Efficiently expand a group using vectorized operations to find similar images.
        This replaces the O(n²) nested loop with optimized numpy operations.
        """
        expanded_group = set(group_images)
        original_size = len(expanded_group)

        # Get indices for all group images
        group_indices = [image_to_idx[img] for img in group_images if img in image_to_idx]
        if not group_indices:
            return expanded_group

        # Get indices for all unprocessed images
        unprocessed_indices = []
        unprocessed_paths = []
        for i, img_path in enumerate(valid_images):
            if img_path not in processed_images and img_path not in expanded_group:
                unprocessed_indices.append(i)
                unprocessed_paths.append(img_path)

        if not unprocessed_indices:
            return expanded_group

        # Use vectorized operations to calculate similarities
        group_indices_array = np.array(group_indices)
        unprocessed_indices_array = np.array(unprocessed_indices)

        # Calculate similarity matrix slice for unprocessed images vs group images
        similarity_slice = similarity_matrix[np.ix_(unprocessed_indices_array, group_indices_array)]

        # Calculate average similarity for each unprocessed image to the group
        avg_similarities = np.mean(similarity_slice, axis=1)

        # Find images that meet the threshold
        similar_mask = avg_similarities >= refinement_threshold
        similar_indices = np.where(similar_mask)[0]

        # Add similar images to the expanded group
        for idx in similar_indices:
            img_path = unprocessed_paths[idx]
            expanded_group.add(img_path)

        if len(expanded_group) > original_size:
            print(f"  ✅ Vectorized expansion: {original_size} -> {len(expanded_group)} images")

        return expanded_group

    def _expand_group_optimized_sampling(self, group_images: List[str], valid_images: List[str],
                                       similarity_matrix: np.ndarray, image_to_idx: Dict[str, int],
                                       processed_images: set, refinement_threshold: float) -> set:
        """
        Expand group using intelligent sampling for very large datasets.
        Uses stratified sampling and early termination for better performance.
        """
        expanded_group = set(group_images)
        original_size = len(expanded_group)

        # Get indices for all group images
        group_indices = [image_to_idx[img] for img in group_images if img in image_to_idx]
        if not group_indices:
            return expanded_group

        # Get unprocessed images
        unprocessed_images = [img for img in valid_images
                            if img not in processed_images and img not in expanded_group]

        if not unprocessed_images:
            return expanded_group

        # For very large datasets, use intelligent sampling
        if len(unprocessed_images) > 1000:
            # Use stratified sampling based on similarity to group centroid
            group_indices_array = np.array(group_indices)
            group_centroid = np.mean(similarity_matrix[group_indices_array], axis=0)

            # Calculate similarities to centroid for all unprocessed images
            unprocessed_indices = [image_to_idx[img] for img in unprocessed_images if img in image_to_idx]
            centroid_similarities = group_centroid[unprocessed_indices]

            # Sort by similarity and take top candidates plus random sample
            sorted_indices = np.argsort(centroid_similarities)[::-1]

            # Take top 30% most similar + random 20% from the rest
            top_count = min(300, len(sorted_indices) // 3)
            random_count = min(200, len(sorted_indices) // 5)

            selected_indices = list(sorted_indices[:top_count])
            if len(sorted_indices) > top_count:
                remaining_indices = sorted_indices[top_count:]
                import random
                random.seed(42)  # Deterministic sampling
                selected_indices.extend(random.sample(list(remaining_indices),
                                                    min(random_count, len(remaining_indices))))

            sampled_images = [unprocessed_images[i] for i in selected_indices]
            print(f"  Intelligent sampling: {len(sampled_images)} from {len(unprocessed_images)} candidates")
        else:
            sampled_images = unprocessed_images

        # Use vectorized operations on sampled images
        sampled_indices = [image_to_idx[img] for img in sampled_images if img in image_to_idx]
        if sampled_indices:
            group_indices_array = np.array(group_indices)
            sampled_indices_array = np.array(sampled_indices)

            # Calculate similarity matrix slice
            similarity_slice = similarity_matrix[np.ix_(sampled_indices_array, group_indices_array)]
            avg_similarities = np.mean(similarity_slice, axis=1)

            # Find similar images
            similar_mask = avg_similarities >= refinement_threshold
            similar_indices = np.where(similar_mask)[0]

            # Add to expanded group
            for idx in similar_indices:
                expanded_group.add(sampled_images[idx])

        if len(expanded_group) > original_size:
            print(f"  ✅ Optimized sampling expansion: {original_size} -> {len(expanded_group)} images")

        return expanded_group

    def _refine_groups_optimized(self, valid_images: List[str], similarity_matrix: np.ndarray, initial_groups: Dict[str, List[str]], monitor: PerformanceMonitor = None) -> Dict[str, List[str]]:
        """
        Optimized refinement for very large datasets.
        Uses sampling and early termination to prevent hanging.
        """
        print("Using optimized refinement for very large dataset...")

        if monitor is None:
            monitor = PerformanceMonitor(len(initial_groups), "Optimized Refinement")
            monitor.set_progress_callback(self.progress_callback)

        # For very large datasets, only refine the largest groups
        # Sort groups by size and only process the top ones
        sorted_groups = sorted(initial_groups.items(), key=lambda x: len(x[1]), reverse=True)

        # Only refine groups with more than 2 images and limit to top 50% of groups
        groups_to_refine = [(rep, group) for rep, group in sorted_groups
                           if len(group) > 2][:len(sorted_groups)//2]

        print(f"Refining {len(groups_to_refine)} largest groups out of {len(initial_groups)} total groups")
        monitor.total_operations = len(groups_to_refine)

        # Start with all original groups
        refined_groups = dict(initial_groups)
        processed_images = set()

        # Create image index lookup for performance
        image_to_idx = {img: idx for idx, img in enumerate(valid_images)}

        # Use a more conservative threshold for very large datasets
        refinement_threshold = max(0.75, self.clip_threshold - 0.02)
        print(f"Using conservative refinement threshold: {refinement_threshold:.3f}")

        for i, (representative, group_images) in enumerate(groups_to_refine):
            # Check for early termination
            if monitor.should_terminate_early():
                print(f"⚠️  Early termination triggered during optimized refinement")
                break

            if representative in processed_images:
                continue

            # Update progress
            group_details = f"Large group: {len(group_images)} images"
            monitor.update_progress(i + 1, group_details)

            # Start with the current group
            expanded_group = set(group_images)

            # Use optimized vectorized expansion with intelligent sampling
            expanded_group = self._expand_group_optimized_sampling(
                group_images, valid_images, similarity_matrix,
                image_to_idx, processed_images, refinement_threshold
            )

            # Update the refined groups
            if len(expanded_group) > len(group_images):
                best_representative = self._find_best_representative(expanded_group, image_to_idx, similarity_matrix)
                refined_groups[best_representative] = list(expanded_group)
                # Remove the old group if representative changed
                if best_representative != representative and representative in refined_groups:
                    del refined_groups[representative]
                print(f"  ✅ Expanded group from {len(group_images)} to {len(expanded_group)} images")

            # Mark all images in this group as processed
            processed_images.update(expanded_group)

        # Final progress update and cleanup
        final_report = monitor.get_final_report()
        print(final_report)
        print(f"Optimized refinement complete: {len(initial_groups)} -> {len(refined_groups)} groups")

        # Memory cleanup for large datasets
        if self.enable_memory_optimization:
            gc.collect()

        return refined_groups
    

    
    def create_batches(self, similar_groups: Dict[str, List[str]], batch_size: int = 4) -> List[List[str]]:
        """
        Create batches of images from similar groups based on similarity.
        Each batch contains similar images with a maximum of batch_size images.
        Uses intelligent splitting to maintain similarity within split batches.

        Args:
            similar_groups: Dictionary of similar image groups
            batch_size: Maximum number of images per batch (used as limit, not target)

        Returns:
            List of batches, each containing similar images (max batch_size per batch)
        """
        batches = []

        print(f"Creating batches from {len(similar_groups)} groups with max batch size {batch_size}")
        total_images = sum(len(group) for group in similar_groups.values())
        print(f"Total images to be processed: {total_images}")

        for representative, group in similar_groups.items():
            # If group has more than batch_size images, split intelligently
            if len(group) > batch_size:
                # Use intelligent splitting that maintains similarity within batches
                split_batches = self._split_group_intelligently(group, batch_size)
                batches.extend(split_batches)
                print(f"Split large group into {len(split_batches)} batches of similar images")
            else:
                # Use the entire group as one batch
                # Include single-image groups as well to ensure all images are processed
                batches.append(group)
                if len(group) == 1:
                    print(f"Created batch {len(batches)} with 1 image (will be preserved as-is)")
                else:
                    print(f"Created batch {len(batches)} with {len(group)} images")

        total_batched_images = sum(len(batch) for batch in batches)
        print(f"Total images in batches: {total_batched_images}")
        print(f"Created {len(batches)} batches total")

        # Validate batches for similarity consistency
        validated_batches = self._validate_batch_similarity(batches)

        # Perform cross-batch validation and merging for large datasets
        if len(validated_batches) > 10:  # Only for datasets with many batches
            final_batches = self._cross_batch_validation_and_merging(validated_batches, batch_size)
        else:
            final_batches = validated_batches

        return final_batches

    def _split_group_intelligently(self, group: List[str], batch_size: int) -> List[List[str]]:
        """
        Split a large group into smaller batches while maintaining similarity within each batch.
        Uses hierarchical clustering approach to ensure similar images stay together.

        Args:
            group: List of image paths in the group
            batch_size: Maximum size for each batch

        Returns:
            List of batches, each containing similar images
        """
        if len(group) <= batch_size:
            return [group]

        # Get similarity matrix for this group
        if not hasattr(self, '_current_similarity_matrix') or not hasattr(self, 'image_paths'):
            print(f"Similarity matrix not available, falling back to sequential splitting for group of {len(group)} images")
            return [group[i:i + batch_size] for i in range(0, len(group), batch_size)]

        group_indices = []
        for img_path in group:
            if img_path in self.image_paths:
                group_indices.append(self.image_paths.index(img_path))

        # If we don't have all indices, fall back to sequential splitting
        if len(group_indices) != len(group):
            print(f"Missing indices for some images, falling back to sequential splitting for group of {len(group)} images")
            return [group[i:i + batch_size] for i in range(0, len(group), batch_size)]

        # Extract sub-similarity matrix for this group
        n = len(group)
        group_similarity = np.zeros((n, n))

        for i, idx1 in enumerate(group_indices):
            for j, idx2 in enumerate(group_indices):
                if i < len(group) and j < len(group):
                    group_similarity[i][j] = self._current_similarity_matrix[idx1][idx2]

        # Use hierarchical clustering to split the group
        try:
            from scipy.cluster.hierarchy import linkage, fcluster
            from scipy.spatial.distance import squareform

            # Convert similarity to distance (1 - similarity)
            distance_matrix = 1 - group_similarity

            # Make sure diagonal is 0 (distance from image to itself)
            np.fill_diagonal(distance_matrix, 0)

            # Convert to condensed distance matrix for linkage
            condensed_distances = squareform(distance_matrix, checks=False)

            # Perform hierarchical clustering
            linkage_matrix = linkage(condensed_distances, method='average')

            # Determine number of clusters needed
            num_clusters = math.ceil(len(group) / batch_size)

            # Get cluster assignments
            cluster_labels = fcluster(linkage_matrix, num_clusters, criterion='maxclust')

            # Group images by cluster
            clusters = defaultdict(list)
            for i, label in enumerate(cluster_labels):
                if i < len(group):
                    clusters[label].append(group[i])

            # Convert clusters to batches, splitting if any cluster is too large
            batches = []
            for cluster_images in clusters.values():
                if len(cluster_images) <= batch_size:
                    batches.append(cluster_images)
                else:
                    # If cluster is still too large, split sequentially
                    for i in range(0, len(cluster_images), batch_size):
                        batches.append(cluster_images[i:i + batch_size])

            print(f"Intelligently split group of {len(group)} into {len(batches)} similarity-based batches")
            return batches

        except ImportError:
            print("SciPy not available, falling back to sequential splitting")
            return [group[i:i + batch_size] for i in range(0, len(group), batch_size)]
        except Exception as e:
            print(f"Error in intelligent splitting: {e}, falling back to sequential splitting")
            return [group[i:i + batch_size] for i in range(0, len(group), batch_size)]

    def _validate_batch_similarity(self, batches: List[List[str]]) -> List[List[str]]:
        """
        Validate that all images in each batch meet minimum similarity requirements.
        Remove or reorganize images that don't meet the threshold.

        Args:
            batches: List of image batches to validate

        Returns:
            List of validated batches with consistent similarity
        """
        if not hasattr(self, '_current_similarity_matrix') or not hasattr(self, 'image_paths'):
            print("Similarity matrix not available, skipping batch validation")
            return batches

        validated_batches = []
        removed_images = []

        print(f"Validating similarity consistency for {len(batches)} batches...")

        for batch_idx, batch in enumerate(batches):
            if len(batch) <= 1:
                # Single image batches are always valid
                validated_batches.append(batch)
                continue

            # Get indices for this batch
            batch_indices = []
            valid_batch_images = []

            for img_path in batch:
                if img_path in self.image_paths:
                    batch_indices.append(self.image_paths.index(img_path))
                    valid_batch_images.append(img_path)

            if len(batch_indices) <= 1:
                validated_batches.append(valid_batch_images)
                continue

            # Calculate average pairwise similarity for this batch
            similarities = []
            for i in range(len(batch_indices)):
                for j in range(i + 1, len(batch_indices)):
                    idx1, idx2 = batch_indices[i], batch_indices[j]
                    similarity = self._current_similarity_matrix[idx1][idx2]
                    similarities.append(similarity)

            avg_similarity = np.mean(similarities) if similarities else 0
            min_similarity = np.min(similarities) if similarities else 0

            # Check if batch meets quality standards
            similarity_threshold = self.clip_threshold * 0.9  # Slightly lower threshold for batch validation

            if min_similarity >= similarity_threshold:
                # Batch is good as-is
                validated_batches.append(valid_batch_images)
                print(f"Batch {batch_idx + 1}: VALID (avg: {avg_similarity:.3f}, min: {min_similarity:.3f})")
            else:
                # Need to filter out problematic images
                print(f"Batch {batch_idx + 1}: NEEDS FILTERING (avg: {avg_similarity:.3f}, min: {min_similarity:.3f})")

                # Find images that don't meet similarity requirements
                good_images = []
                bad_images = []

                for i, img_path in enumerate(valid_batch_images):
                    # Check this image's similarity to all others in the batch
                    img_similarities = []
                    for j, other_img in enumerate(valid_batch_images):
                        if i != j:
                            idx1, idx2 = batch_indices[i], batch_indices[j]
                            img_similarities.append(self._current_similarity_matrix[idx1][idx2])

                    avg_img_similarity = np.mean(img_similarities) if img_similarities else 1.0

                    if avg_img_similarity >= similarity_threshold:
                        good_images.append(img_path)
                    else:
                        bad_images.append(img_path)
                        print(f"  Removing {os.path.basename(img_path)} (avg similarity: {avg_img_similarity:.3f})")

                if len(good_images) > 0:
                    validated_batches.append(good_images)
                    print(f"  Kept {len(good_images)} images in batch")

                # Add problematic images to removed list for potential reprocessing
                removed_images.extend(bad_images)

        # Try to create new batches from removed images
        if removed_images:
            print(f"Attempting to reprocess {len(removed_images)} removed images...")
            # Create single-image batches for removed images
            for img_path in removed_images:
                validated_batches.append([img_path])
                print(f"Created single-image batch for: {os.path.basename(img_path)}")

        print(f"Validation complete: {len(validated_batches)} validated batches")
        return validated_batches

    def _cross_batch_validation_and_merging(self, batches: List[List[str]], max_batch_size: int) -> List[List[str]]:
        """
        Perform cross-batch validation to find and merge similar images that ended up in different batches.
        This is the final step to ensure no similar images are missed across the entire dataset.

        Args:
            batches: List of validated batches
            max_batch_size: Maximum allowed batch size

        Returns:
            List of final batches with cross-batch similarities resolved
        """
        if not hasattr(self, '_current_similarity_matrix') or not hasattr(self, 'image_paths'):
            print("Similarity matrix not available, skipping cross-batch validation")
            return batches

        print(f"Performing cross-batch validation for {len(batches)} batches...")

        # Create a cross-batch similarity threshold (slightly lower than main threshold)
        cross_batch_threshold = max(0.75, self.clip_threshold - 0.03)

        # Track which batches have been merged
        merged_batches = []
        processed_batch_indices = set()

        for i, batch_a in enumerate(batches):
            if i in processed_batch_indices:
                continue

            # Start with the current batch
            current_merged_batch = list(batch_a)
            processed_batch_indices.add(i)

            # Check this batch against all other unprocessed batches
            for j, batch_b in enumerate(batches):
                if j <= i or j in processed_batch_indices:
                    continue

                # Check if any images in batch_a are similar to any images in batch_b
                should_merge, merge_reason = self._should_merge_batches(
                    batch_a, batch_b, cross_batch_threshold
                )

                if should_merge:
                    # Check if merged batch would be too large
                    potential_size = len(current_merged_batch) + len(batch_b)

                    if potential_size <= max_batch_size * 1.5:  # Allow some flexibility
                        current_merged_batch.extend(batch_b)
                        processed_batch_indices.add(j)
                        print(f"Cross-batch merge: Combined batches {i+1} and {j+1} ({merge_reason})")
                    else:
                        print(f"Skipping merge of batches {i+1} and {j+1}: would create batch of size {potential_size}")

            # Add the (possibly merged) batch to results
            if len(current_merged_batch) <= max_batch_size:
                merged_batches.append(current_merged_batch)
            else:
                # If merged batch is too large, split it intelligently
                split_batches = self._split_group_intelligently(current_merged_batch, max_batch_size)
                merged_batches.extend(split_batches)
                print(f"Split oversized merged batch into {len(split_batches)} batches")

        print(f"Cross-batch validation complete: {len(batches)} -> {len(merged_batches)} final batches")
        return merged_batches

    def _should_merge_batches(self, batch_a: List[str], batch_b: List[str], threshold: float) -> Tuple[bool, str]:
        """
        Determine if two batches should be merged based on cross-batch similarity.

        Args:
            batch_a: First batch of images
            batch_b: Second batch of images
            threshold: Similarity threshold for merging

        Returns:
            Tuple of (should_merge: bool, reason: str)
        """
        if not batch_a or not batch_b:
            return False, "Empty batch"

        # Get indices for both batches
        indices_a = []
        indices_b = []

        for img_path in batch_a:
            if img_path in self.image_paths:
                indices_a.append(self.image_paths.index(img_path))

        for img_path in batch_b:
            if img_path in self.image_paths:
                indices_b.append(self.image_paths.index(img_path))

        if not indices_a or not indices_b:
            return False, "Missing indices"

        # Calculate cross-batch similarities
        cross_similarities = []
        for idx_a in indices_a:
            for idx_b in indices_b:
                similarity = self._current_similarity_matrix[idx_a][idx_b]
                cross_similarities.append(similarity)

        if not cross_similarities:
            return False, "No similarities calculated"

        max_similarity = np.max(cross_similarities)
        avg_similarity = np.mean(cross_similarities)
        high_similarity_count = sum(1 for s in cross_similarities if s >= threshold)

        # Merge criteria
        if max_similarity >= threshold + 0.05:  # Very high similarity
            return True, f"high max similarity: {max_similarity:.3f}"

        if avg_similarity >= threshold and high_similarity_count >= 2:  # Good average with multiple connections
            return True, f"good average similarity: {avg_similarity:.3f} with {high_similarity_count} connections"

        # For single-image batches, be more lenient
        if len(batch_a) == 1 or len(batch_b) == 1:
            if max_similarity >= threshold - 0.02:
                return True, f"single-image batch with similarity: {max_similarity:.3f}"

        return False, f"insufficient similarity (max: {max_similarity:.3f}, avg: {avg_similarity:.3f})"

    def _calculate_chunked_similarity(self, feature_matrix: np.ndarray, chunk_size: int = None) -> np.ndarray:
        """
        Calculate similarity matrix in chunks to manage memory for large datasets.
        Uses adaptive chunk sizing and memory monitoring.

        Args:
            feature_matrix: Matrix of image features
            chunk_size: Size of chunks to process at once (auto-calculated if None)

        Returns:
            Full similarity matrix
        """
        n = len(feature_matrix)

        # Auto-calculate optimal chunk size based on available memory and dataset size
        if chunk_size is None:
            chunk_size = self._calculate_optimal_chunk_size(n)

        print(f"Calculating similarity matrix in chunks of {chunk_size} for {n} images...")

        # Initialize performance monitor
        total_chunks = math.ceil(n / chunk_size) ** 2
        monitor = PerformanceMonitor(total_chunks, "Similarity Calculation")
        monitor.set_progress_callback(self.progress_callback)

        # Use memory-mapped array for very large datasets
        if n > 2000 and self.enable_memory_optimization:
            similarity_matrix = np.memmap('temp_similarity.dat', dtype='float32', mode='w+', shape=(n, n))
        else:
            similarity_matrix = np.zeros((n, n), dtype=np.float32)

        chunk_count = 0

        # Calculate similarity in chunks
        for i in range(0, n, chunk_size):
            i_end = min(i + chunk_size, n)

            for j in range(0, n, chunk_size):
                j_end = min(j + chunk_size, n)
                chunk_count += 1

                # Calculate similarity for this chunk
                chunk_similarity = cosine_similarity(
                    feature_matrix[i:i_end],
                    feature_matrix[j:j_end]
                )

                # Store in the full matrix
                similarity_matrix[i:i_end, j:j_end] = chunk_similarity.astype(np.float32)

                # Update progress
                monitor.update_progress(chunk_count, f"Chunk ({i//chunk_size + 1}, {j//chunk_size + 1})")

                # Force garbage collection periodically for large datasets
                if self.enable_memory_optimization and chunk_count % 50 == 0:
                    gc.collect()

        final_report = monitor.get_final_report()
        print(final_report)
        return similarity_matrix

    def _calculate_optimal_chunk_size(self, n: int) -> int:
        """
        Calculate optimal chunk size based on available memory and dataset size.

        Args:
            n: Number of images

        Returns:
            Optimal chunk size
        """
        if PSUTIL_AVAILABLE:
            try:
                # Get available memory
                available_memory_gb = psutil.virtual_memory().available / (1024**3)

                # Estimate memory needed for full similarity matrix (in GB)
                full_matrix_memory_gb = (n * n * 4) / (1024**3)  # 4 bytes per float32

                # If we have enough memory for full matrix, use larger chunks
                if available_memory_gb > full_matrix_memory_gb * 2:
                    return min(500, n // 4)
                elif available_memory_gb > full_matrix_memory_gb:
                    return min(300, n // 6)
                else:
                    return min(200, n // 10)
            except:
                pass

        # Fallback chunk size calculation
        if n > 2000:
            return 100
        elif n > 1000:
            return 200
        else:
            return 300

    def get_memory_usage_estimate(self, num_images: int) -> str:
        """
        Estimate memory usage for processing a given number of images.

        Args:
            num_images: Number of images to process

        Returns:
            Human-readable memory estimate
        """
        # Rough estimates based on typical usage
        feature_size_mb = num_images * 0.5  # ~0.5MB per image for CLIP features
        similarity_matrix_mb = (num_images ** 2) * 4 / (1024 * 1024)  # 4 bytes per float32
        total_mb = feature_size_mb + similarity_matrix_mb

        if total_mb < 1024:
            return f"~{total_mb:.1f} MB"
        else:
            return f"~{total_mb/1024:.1f} GB"

    def optimize_for_dataset_size(self, num_images: int) -> Dict[str, any]:
        """
        Suggest optimal settings based on dataset size and available resources.
        Now includes dynamic performance scaling based on system capabilities.

        Args:
            num_images: Number of images in the dataset

        Returns:
            Dictionary of recommended settings with performance optimizations
        """
        # Get system information for dynamic scaling
        system_info = self._get_system_capabilities()

        # Base recommendations by dataset size
        if num_images < 100:
            base_settings = {
                "batch_size": 4,
                "clip_threshold": 0.85,
                "enable_cross_batch_validation": False,
                "enable_global_refinement": False,
                "chunk_size": None,
                "max_refinement_time_minutes": 5
            }
        elif num_images < 500:
            base_settings = {
                "batch_size": 6,
                "clip_threshold": 0.82,
                "enable_cross_batch_validation": True,
                "enable_global_refinement": True,
                "chunk_size": 300,
                "max_refinement_time_minutes": 10
            }
        elif num_images < 1000:
            base_settings = {
                "batch_size": 8,
                "clip_threshold": 0.80,
                "enable_cross_batch_validation": True,
                "enable_global_refinement": True,
                "chunk_size": 200,
                "max_refinement_time_minutes": 15
            }
        else:
            base_settings = {
                "batch_size": 10,
                "clip_threshold": 0.78,
                "enable_cross_batch_validation": True,
                "enable_global_refinement": True,
                "chunk_size": 150,
                "max_refinement_time_minutes": 20
            }

        # Apply dynamic scaling based on system capabilities
        scaled_settings = self._apply_dynamic_scaling(base_settings, num_images, system_info)
        return scaled_settings

    def _get_system_capabilities(self) -> Dict[str, any]:
        """Get information about system capabilities for performance scaling."""
        capabilities = {
            "available_memory_gb": 8.0,  # Default fallback
            "cpu_count": 4,  # Default fallback
            "has_gpu": False,
            "gpu_memory_gb": 0.0
        }

        if PSUTIL_AVAILABLE:
            try:
                # Get memory information
                memory = psutil.virtual_memory()
                capabilities["available_memory_gb"] = memory.available / (1024**3)
                capabilities["cpu_count"] = psutil.cpu_count()
            except:
                pass

        # Check GPU availability
        try:
            if hasattr(torch, 'cuda') and torch.cuda.is_available():
                capabilities["has_gpu"] = True
                capabilities["gpu_memory_gb"] = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                capabilities["has_gpu"] = True
                capabilities["gpu_memory_gb"] = 8.0  # Estimate for Apple Silicon
        except:
            pass

        return capabilities

    def _apply_dynamic_scaling(self, base_settings: Dict[str, any], num_images: int,
                             system_info: Dict[str, any]) -> Dict[str, any]:
        """Apply dynamic scaling based on system capabilities."""
        scaled_settings = base_settings.copy()

        # Memory-based scaling
        available_memory = system_info["available_memory_gb"]
        estimated_memory_needed = self._estimate_memory_requirements(num_images)

        if available_memory < estimated_memory_needed:
            # Reduce memory usage
            print(f"⚠️  Limited memory detected ({available_memory:.1f}GB available, {estimated_memory_needed:.1f}GB estimated)")
            scaled_settings["chunk_size"] = min(100, scaled_settings.get("chunk_size", 200))
            scaled_settings["enable_cross_batch_validation"] = False
            if num_images > 1000:
                scaled_settings["enable_global_refinement"] = False
                print("  Disabled global refinement due to memory constraints")
        elif available_memory > estimated_memory_needed * 2:
            # Increase performance with abundant memory
            if scaled_settings.get("chunk_size"):
                scaled_settings["chunk_size"] = min(500, scaled_settings["chunk_size"] * 2)

        # GPU-based scaling
        if system_info["has_gpu"]:
            # Increase batch sizes for GPU processing
            scaled_settings["batch_size"] = min(16, scaled_settings["batch_size"] + 2)
            if system_info["gpu_memory_gb"] > 8:
                scaled_settings["chunk_size"] = scaled_settings.get("chunk_size", 200) * 2

        # CPU-based scaling
        cpu_count = system_info["cpu_count"]
        if cpu_count > 8:
            # High-end CPU - can handle more aggressive processing
            scaled_settings["max_refinement_time_minutes"] *= 1.5
        elif cpu_count < 4:
            # Low-end CPU - be more conservative
            scaled_settings["max_refinement_time_minutes"] *= 0.7
            if num_images > 500:
                scaled_settings["enable_cross_batch_validation"] = False

        # Add performance strategy
        if num_images > 2000:
            scaled_settings["performance_strategy"] = "ultra_large"
        elif num_images > 1000:
            scaled_settings["performance_strategy"] = "large"
        elif num_images > 500:
            scaled_settings["performance_strategy"] = "medium"
        else:
            scaled_settings["performance_strategy"] = "standard"

        return scaled_settings

    def _estimate_memory_requirements(self, num_images: int) -> float:
        """Estimate memory requirements in GB for processing."""
        # Rough estimates based on typical usage
        feature_memory = (num_images * 512 * 4) / (1024**3)  # CLIP features
        similarity_matrix_memory = (num_images ** 2 * 4) / (1024**3)  # Similarity matrix
        overhead = 2.0  # OS and other overhead

        return feature_memory + similarity_matrix_memory + overhead
    
    def process_directory(self, input_dir: str, output_dir: str, batch_size: int = 4, 
                         target_size: Optional[Tuple[int, int]] = (300, 300), layout_mode: str = "grid", 
                         spacing: int = 15, background_color: str = "white", quality: int = 95,
                         clip_threshold: float = None, generate_master_file: bool = False):
        """
        Process a directory to find variations and create combined images.
        
        Args:
            input_dir: Input directory containing images
            output_dir: Output directory for combined images
            batch_size: Number of images per batch
            target_size: Target size for individual images (width, height), or None for dynamic sizing
            layout_mode: Layout mode ('grid', 'horizontal', 'vertical')
            spacing: Spacing between images in pixels
            background_color: Background color for combined images
            quality: JPEG quality (70-100)
            clip_threshold: Override clip_threshold for this operation
            generate_master_file: Whether to generate a master text file with all batch info
        """
        # Override settings if provided
        if clip_threshold is not None:
            original_clip_threshold = self.clip_threshold
            self.clip_threshold = clip_threshold
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Scan for images
        print(f"Scanning directory: {input_dir}")
        image_paths = self.scan_directory(input_dir)
        
        if not image_paths:
            print("No images found in the specified directory")
            return []
        
        print(f"Found {len(image_paths)} image files")
        
        # Track all processed images to handle unprocessed ones later
        all_processed_images = set()
        
        # Find similar images
        similar_groups = self.find_similar_images(image_paths)
        
        if not similar_groups:
            print("No similar image groups found")
            # Don't return yet, process individual images below
        else:
            print(f"Found {len(similar_groups)} groups of similar images")
        
        # Create batches
        batches = self.create_batches(similar_groups, batch_size)
        
        if not batches:
            print("No valid batches created")
            # Don't return yet, process individual images below
        else:
            print(f"Created {len(batches)} batches for processing")
        
        # Combine images in each batch
        combiner = ImageCombiner(spacing=spacing, background_color=background_color)
        
        batch_counter = 0
        
        for i, batch in enumerate(batches, 1):
            try:
                print(f"\nProcessing batch {i}/{len(batches)} ({len(batch)} images)...")
                
                # Add all images in this batch to the processed set
                for img_path in batch:
                    all_processed_images.add(img_path)
                
                # For single-image batches, preserve the image as-is without combining
                if len(batch) == 1:
                    img_path = batch[0]
                    batch_counter += 1
                    
                    # Save image with original format, maintaining exact same image and size
                    original_format = os.path.splitext(img_path)[1].lower()
                    if original_format in ['.png', '.gif', '.webp', '.tiff', '.tif'] or background_color.lower() == "transparent":
                        output_filename = f"variation_batch_{batch_counter:03d}{original_format}"
                    else:
                        output_filename = f"variation_batch_{batch_counter:03d}.jpg"
                    
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Copy the original file directly to preserve the exact image with its original size
                    shutil.copy2(img_path, output_path)
                    
                    print(f"Saved individual image as-is: {output_filename} (original size preserved)")
                    
                    # Create a text file listing the source image
                    info_filename = f"variation_batch_{batch_counter:03d}_sources.txt"
                    info_path = os.path.join(output_dir, info_filename)
                    with open(info_path, 'w') as f:
                        f.write(f"Batch {batch_counter} - Source Image:\n")
                        f.write("=" * 30 + "\n")
                        f.write(f"1. {os.path.relpath(img_path, input_dir)}\n")
                    
                    continue  # Skip the rest of the loop for single images
                
                # For multi-image batches, combine them as before
                # Load images
                images = combiner.load_images(batch)
                
                if not images:
                    print(f"Failed to load images for batch {i}")
                    continue
                
                # Resize to uniform size
                images = combiner.resize_images_uniform(images, target_size=target_size)
                
                # Combine images based on layout mode
                if layout_mode == "grid":
                    combined = combiner.combine_grid(images)  # Let it auto-determine optimal layout
                elif layout_mode == "horizontal":
                    combined = combiner.combine_horizontal(images)
                elif layout_mode == "vertical":
                    combined = combiner.combine_vertical(images)
                else:
                    combined = combiner.combine_grid(images)  # Default to grid with optimal layout
                
                # Save combined image
                # Use PNG for transparent backgrounds, JPG for others
                batch_counter += 1
                if background_color.lower() == "transparent":
                    output_filename = f"variation_batch_{batch_counter:03d}.png"
                    output_path = os.path.join(output_dir, output_filename)
                    combined.save(output_path)
                else:
                    output_filename = f"variation_batch_{batch_counter:03d}.jpg"
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Convert RGBA to RGB if needed for JPEG format
                    if combined.mode == 'RGBA':
                        # Create a white background and paste the RGBA image onto it
                        rgb_image = Image.new('RGB', combined.size, (255, 255, 255))
                        rgb_image.paste(combined, mask=combined.split()[-1])  # Use alpha channel as mask
                        rgb_image.save(output_path, quality=quality)
                    else:
                        combined.save(output_path, quality=quality)
                
                print(f"Saved: {output_filename} ({combined.size[0]}x{combined.size[1]})")
                
                # Create a text file listing the source images
                info_filename = f"variation_batch_{batch_counter:03d}_sources.txt"
                info_path = os.path.join(output_dir, info_filename)
                with open(info_path, 'w') as f:
                    f.write(f"Batch {batch_counter} - Source Images:\n")
                    f.write("=" * 30 + "\n")
                    for j, img_path in enumerate(batch, 1):
                        f.write(f"{j}. {os.path.relpath(img_path, input_dir)}\n")
                
            except Exception as e:
                print(f"Error processing batch {i}: {e}")
                continue
        
        # Process any unprocessed images individually (no similarity match)
        unprocessed = [path for path in image_paths if path not in all_processed_images]
        if unprocessed:
            print(f"\nProcessing {len(unprocessed)} remaining images with no similarity matches...")
            
            # Process each unprocessed image individually
            for i, img_path in enumerate(unprocessed):
                try:
                    # Create a batch with just this one image
                    single_batch = [img_path]
                    
                    # Since it's a single image with no matches, preserve it exactly as is
                    batch_counter += 1
                    
                    # Save image with original format, maintaining exact same image and size
                    original_format = os.path.splitext(img_path)[1].lower()
                    if original_format in ['.png', '.gif', '.webp', '.tiff', '.tif'] or background_color.lower() == "transparent":
                        output_filename = f"variation_batch_{batch_counter:03d}{original_format}"
                    else:
                        output_filename = f"variation_batch_{batch_counter:03d}.jpg"
                    
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Copy the original file directly to preserve the exact image with its original size
                    shutil.copy2(img_path, output_path)
                    
                    print(f"Saved individual image as-is: {output_filename} (original size preserved)")
                    
                    # Create a text file listing the source image
                    info_filename = f"variation_batch_{batch_counter:03d}_sources.txt"
                    info_path = os.path.join(output_dir, info_filename)
                    with open(info_path, 'w') as f:
                        f.write(f"Batch {batch_counter} - Source Image:\n")
                        f.write("=" * 30 + "\n")
                        f.write(f"1. {os.path.relpath(img_path, input_dir)}\n")
                        
                except Exception as e:
                    print(f"Error processing individual image {img_path}: {e}")
                    continue
        
        # Generate a master text file with all batch information if requested
        if generate_master_file:
            master_filename = "all_batches_info.txt"
            master_path = os.path.join(output_dir, master_filename)
            with open(master_path, 'w') as f:
                f.write(f"Image Variation Batches - Master File\n")
                f.write(f"Total images processed: {len(image_paths)}\n")
                f.write(f"Total batches created: {batch_counter}\n")
                f.write(f"Similarity threshold: {self.clip_threshold}\n")
                f.write(f"CLIP model: {self.clip_model_name}\n")
                f.write("=" * 60 + "\n\n")
                
                # Write info about each batch
                for i in range(1, batch_counter + 1):
                    batch_file_path = os.path.join(output_dir, f"variation_batch_{i:03d}_sources.txt")
                    if os.path.exists(batch_file_path):
                        f.write(f"Batch #{i:03d}\n")
                        f.write("-" * 30 + "\n")
                        
                        # Copy content from the individual batch file
                        with open(batch_file_path, 'r') as batch_file:
                            # Skip the first two lines (header)
                            next(batch_file)  # Skip "Batch X - Source Images:"
                            next(batch_file)  # Skip "===================="
                            
                            # Copy the rest of the file
                            for line in batch_file:
                                f.write(line)
                        
                        f.write("\n")
            
            print(f"Generated master text file: {master_filename}")
        
        # Restore original settings if they were overridden
        if clip_threshold is not None:
            self.clip_threshold = original_clip_threshold
            
        print(f"\nProcessing complete! Check the output directory: {output_dir}")
        print(f"Total batches created: {batch_counter}")
        return batches

def main():
    parser = argparse.ArgumentParser(description="Automatically find image variations and combine them into batches using CLIP")
    parser.add_argument("input_dir", help="Input directory containing images")
    parser.add_argument("-o", "--output", default="variation_batches", help="Output directory for combined images")
    parser.add_argument("-b", "--batch-size", type=int, default=4, help="Number of images per batch")
    parser.add_argument("--clip-threshold", type=float, default=0.85, help="CLIP similarity threshold (0.0-1.0, higher = more strict)")
    parser.add_argument("--clip-model", default="ViT-B/32", help="CLIP model to use (ViT-B/32, ViT-L/14, RN50, etc.)")
    parser.add_argument("--device", default="auto", choices=["auto", "cpu", "gpu", "mps", "cuda"], help="Device to use for processing (auto, cpu, gpu, mps, cuda)")
    parser.add_argument("--extensions", nargs="+", help="File extensions to include (e.g., .jpg .png)")
    parser.add_argument("--master-file", action="store_true", help="Generate a master text file with all batch information")
    
    args = parser.parse_args()
    
    if not os.path.isdir(args.input_dir):
        print(f"Error: Input directory '{args.input_dir}' does not exist")
        sys.exit(1)
    
    # Create variation finder
    finder = ImageVariationFinder(
        clip_threshold=args.clip_threshold,
        clip_model_name=args.clip_model,
        device=args.device
    )
    
    # Process directory
    finder.process_directory(
        input_dir=args.input_dir,
        output_dir=args.output,
        batch_size=args.batch_size,
        generate_master_file=args.master_file
    )

if __name__ == "__main__":
    main()