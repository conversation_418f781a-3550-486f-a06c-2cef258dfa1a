#!/usr/bin/env python3
"""
Test script to verify that the batch size fix works correctly.
This script tests that user's manually chosen batch size is always respected.
"""

import os
import sys
import tempfile
import shutil
from PIL import Image
import numpy as np

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from auto_variation_finder import ImageVariationFinder


def create_test_images(num_images: int, output_dir: str):
    """Create test images for testing batch size behavior."""
    print(f"Creating {num_images} test images in {output_dir}")
    
    # Create groups of similar images
    images_per_group = 5
    num_groups = max(1, num_images // images_per_group)
    
    for group_id in range(num_groups):
        # Create a base color for this group
        base_color = (
            (group_id * 50) % 255,
            (group_id * 80) % 255, 
            (group_id * 120) % 255
        )
        
        for img_id in range(images_per_group):
            if group_id * images_per_group + img_id >= num_images:
                break
                
            # Create slight variations of the base color
            color = (
                max(0, min(255, base_color[0] + np.random.randint(-20, 21))),
                max(0, min(255, base_color[1] + np.random.randint(-20, 21))),
                max(0, min(255, base_color[2] + np.random.randint(-20, 21)))
            )
            
            # Create image
            img = Image.new('RGB', (100, 100), color)
            img_path = os.path.join(output_dir, f"test_group_{group_id:02d}_img_{img_id:02d}.png")
            img.save(img_path)
    
    print(f"Created {num_images} test images in {num_groups} similarity groups")


def test_batch_size_respect(test_dir: str, num_images: int, user_batch_size: int):
    """Test that the user's batch size is respected."""
    print(f"\n=== Testing Batch Size Respect ===")
    print(f"Dataset size: {num_images} images")
    print(f"User's chosen batch size: {user_batch_size}")
    
    # Initialize finder
    finder = ImageVariationFinder(
        clip_threshold=0.75,
        clip_model_name="ViT-B/32",
        device="auto"
    )
    
    # Get all image paths
    image_paths = []
    for filename in os.listdir(test_dir):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            image_paths.append(os.path.join(test_dir, filename))
    
    print(f"Found {len(image_paths)} images")
    
    # Get recommendations (this is what the GUI would get)
    recommendations = finder.optimize_for_dataset_size(len(image_paths))
    print(f"System recommendations: {recommendations}")
    
    # Find similar images
    print("Finding similar images...")
    similar_groups = finder.find_similar_images(image_paths)
    print(f"Found {len(similar_groups)} similarity groups")
    
    # Create batches with user's chosen batch size
    print(f"Creating batches with user's batch size: {user_batch_size}")
    batches = finder.create_batches(similar_groups, user_batch_size)
    
    print(f"Created {len(batches)} batches")
    
    # Verify batch sizes
    batch_sizes = [len(batch) for batch in batches]
    max_batch_size = max(batch_sizes) if batch_sizes else 0
    
    print(f"Batch sizes: {batch_sizes}")
    print(f"Maximum batch size: {max_batch_size}")
    
    # Test results
    success = True
    if max_batch_size > user_batch_size:
        print(f"❌ FAIL: Found batch with {max_batch_size} images, exceeds user's limit of {user_batch_size}")
        success = False
    else:
        print(f"✅ PASS: All batches respect user's batch size limit of {user_batch_size}")
    
    # Check that we're not wasting efficiency by making batches too small
    avg_batch_size = sum(batch_sizes) / len(batch_sizes) if batch_sizes else 0
    print(f"Average batch size: {avg_batch_size:.1f}")
    
    if avg_batch_size < user_batch_size * 0.5:
        print(f"⚠️  WARNING: Average batch size is much smaller than user's limit. Consider optimizing batch creation.")
    
    return success


def main():
    """Run comprehensive tests for the batch size fix."""
    print("Testing Batch Size Fix")
    print("=" * 50)
    
    # Test scenarios
    test_scenarios = [
        {"num_images": 50, "user_batch_size": 2, "description": "Small dataset, small batch size"},
        {"num_images": 100, "user_batch_size": 3, "description": "Medium dataset, small batch size"},
        {"num_images": 600, "user_batch_size": 2, "description": "Large dataset (>500), very small batch size"},
        {"num_images": 600, "user_batch_size": 4, "description": "Large dataset (>500), normal batch size"},
        {"num_images": 1200, "user_batch_size": 3, "description": "Very large dataset (>1000), small batch size"},
    ]
    
    all_passed = True
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{'='*60}")
        print(f"Test {i}/{len(test_scenarios)}: {scenario['description']}")
        print(f"{'='*60}")
        
        # Create temporary directory for test images
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test images
            create_test_images(scenario['num_images'], temp_dir)
            
            # Test batch size respect
            passed = test_batch_size_respect(
                temp_dir, 
                scenario['num_images'], 
                scenario['user_batch_size']
            )
            
            if not passed:
                all_passed = False
                print(f"❌ Test {i} FAILED")
            else:
                print(f"✅ Test {i} PASSED")
    
    print(f"\n{'='*60}")
    print("FINAL RESULTS")
    print(f"{'='*60}")
    
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ User's batch size is properly respected in all scenarios")
        print("✅ The fix successfully prevents automatic batch size adjustment")
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️  The batch size fix may need additional work")
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    sys.exit(main())
